#!/bin/bash

echo "🚀 Starting YouTube Thumbnail Generator with Secure API Protection"
echo ""
echo "Installing dependencies if needed..."
npm install
echo ""
echo "Starting both frontend and backend servers..."
echo "Frontend will be available at: http://localhost:3000"
echo "Backend API will be available at: http://localhost:3001"
echo ""
echo "Press Ctrl+C to stop both servers"
echo ""
npm run dev:full
