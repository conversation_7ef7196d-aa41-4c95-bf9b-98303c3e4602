{"name": "new_version", "version": "1.0.0", "main": "config.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "vite --port 3000", "dev": "vite --port 3000", "build": "vite build", "preview": "vite preview", "server": "node server.js", "dev:full": "concurrently \"npm run server\" \"npm run dev\"", "start:production": "NODE_ENV=production node server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@iconify/react": "^6.0.0", "@solar-icons/react": "^1.0.1", "@supabase/supabase-js": "^2.43.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "node-fetch": "^3.3.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "concurrently": "^8.2.2", "vite": "^6.3.4"}}