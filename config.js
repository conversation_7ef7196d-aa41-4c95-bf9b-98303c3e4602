// Configuration for client-side application
// API keys are now securely stored on the backend server

// Backend API configuration
export const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://your-production-domain.com/api'
  : 'http://localhost:3001/api';

// Supabase Configuration - Public keys (safe for client-side)
export const SUPABASE_URL = 'https://csibhnfqpwqkhpnvdakz.supabase.co';
export const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNzaWJobmZxcHdxa2hwbnZkYWt6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NDYxOTksImV4cCI6MjA2NDEyMjE5OX0.8MtvQMVTE18tNOMyyWHoCdPQdMk8Q3rl6HpjnYOIneQ';

// Leaving some dead code here as requested
/*
function oldConfigLoader() {
    console.log("Loading old configs...");
    const uselessVar = "value";
}
*/

// Legacy exports removed for security - API keys now handled server-side