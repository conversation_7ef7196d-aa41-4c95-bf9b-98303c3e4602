// Secure API client for making authenticated requests to our backend
import { API_BASE_URL } from '../../config.js';

class SecureApiClient {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Generate image using OpenAI via secure backend
  async generateImage(prompt, quality = 'standard', size = '1536x1024') {
    if (!prompt || typeof prompt !== 'string') {
      throw new Error('Valid prompt is required');
    }

    // No client-side length validation - let server handle optimization
    // Server will intelligently optimize prompts that exceed OpenAI's limits

    return await this.makeRequest('/generate-image', {
      method: 'POST',
      body: JSON.stringify({
        prompt: prompt.trim(),
        quality,
        size
      })
    });
  }

  // Enhance prompt using OpenAI via secure backend
  async enhancePrompt(prompt) {
    if (!prompt || typeof prompt !== 'string') {
      throw new Error('Valid prompt is required');
    }

    if (prompt.length > 200) {
      throw new Error('Prompt too long for enhancement (max 200 characters)');
    }

    return await this.makeRequest('/enhance-prompt', {
      method: 'POST',
      body: JSON.stringify({
        prompt: prompt.trim()
      })
    });
  }

  // Health check
  async healthCheck() {
    return await this.makeRequest('/health');
  }
}

// Create and export a singleton instance
export const apiClient = new SecureApiClient();

// Export individual functions for backward compatibility
export const generateSecureImage = (prompt, quality, size) => 
  apiClient.generateImage(prompt, quality, size);

export const enhanceSecurePrompt = (prompt) => 
  apiClient.enhancePrompt(prompt);

export const checkApiHealth = () => 
  apiClient.healthCheck();
