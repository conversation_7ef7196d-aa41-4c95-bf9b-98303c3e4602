const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true
}));

// Rate limiting middleware
const rateLimit = {};
const RATE_LIMIT_WINDOW = parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000; // 15 minutes
const RATE_LIMIT_MAX = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100;

const rateLimitMiddleware = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  
  if (!rateLimit[clientIP]) {
    rateLimit[clientIP] = { count: 1, resetTime: now + RATE_LIMIT_WINDOW };
  } else if (now > rateLimit[clientIP].resetTime) {
    rateLimit[clientIP] = { count: 1, resetTime: now + RATE_LIMIT_WINDOW };
  } else {
    rateLimit[clientIP].count++;
  }
  
  if (rateLimit[clientIP].count > RATE_LIMIT_MAX) {
    return res.status(429).json({
      error: 'Too many requests',
      message: 'Rate limit exceeded. Please try again later.',
      resetTime: rateLimit[clientIP].resetTime
    });
  }
  
  next();
};

// Apply rate limiting to API routes
app.use('/api', rateLimitMiddleware);

// Validation middleware
const validateOpenAIRequest = (req, res, next) => {
  const { prompt, quality, size } = req.body;
  
  if (!prompt || typeof prompt !== 'string') {
    return res.status(400).json({ error: 'Valid prompt is required' });
  }
  
  if (prompt.length > 8000) {
    return res.status(400).json({ error: 'Prompt too long (max 8000 characters)' });
  }
  
  const validQualities = ['standard', 'hd', 'low', 'medium', 'high'];
  if (quality && !validQualities.includes(quality)) {
    return res.status(400).json({ error: 'Invalid quality parameter' });
  }
  
  const validSizes = ['1024x1024', '1792x1024', '1024x1792', '1536x1024'];
  if (size && !validSizes.includes(size)) {
    return res.status(400).json({ error: 'Invalid size parameter' });
  }
  
  next();
};

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Helper function to optimize prompt length for OpenAI
const optimizePrompt = (prompt) => {
  // OpenAI's actual limit is around 4000 characters for gpt-image-1
  const MAX_OPENAI_PROMPT_LENGTH = 4000;

  if (prompt.length <= MAX_OPENAI_PROMPT_LENGTH) {
    return prompt;
  }

  console.log(`Prompt too long (${prompt.length} chars), optimizing...`);

  // Try to preserve the most important parts of the prompt
  // Keep the beginning (main instructions) and try to preserve text overlay sections
  const lines = prompt.split('\n');
  let optimizedPrompt = '';
  let currentLength = 0;

  // Priority order: main instructions, text overlay, then other details
  const priorityKeywords = ['Create a cinematic', 'Text Overlay:', 'resolution', 'YouTube thumbnail'];

  // First pass: add high-priority lines
  for (const line of lines) {
    const lineWithNewline = line + '\n';
    if (currentLength + lineWithNewline.length <= MAX_OPENAI_PROMPT_LENGTH) {
      const isHighPriority = priorityKeywords.some(keyword => line.includes(keyword));
      if (isHighPriority || optimizedPrompt.length < 1000) {
        optimizedPrompt += lineWithNewline;
        currentLength += lineWithNewline.length;
      }
    }
  }

  // Second pass: add remaining lines if space allows
  for (const line of lines) {
    const lineWithNewline = line + '\n';
    if (currentLength + lineWithNewline.length <= MAX_OPENAI_PROMPT_LENGTH && !optimizedPrompt.includes(line)) {
      optimizedPrompt += lineWithNewline;
      currentLength += lineWithNewline.length;
    }
  }

  // If still too long, truncate and add continuation indicator
  if (optimizedPrompt.length > MAX_OPENAI_PROMPT_LENGTH) {
    optimizedPrompt = optimizedPrompt.substring(0, MAX_OPENAI_PROMPT_LENGTH - 50) + '... [optimized for length]';
  }

  console.log(`Prompt optimized from ${prompt.length} to ${optimizedPrompt.length} characters`);
  return optimizedPrompt;
};

// OpenAI Image Generation endpoint
app.post('/api/generate-image', validateOpenAIRequest, async (req, res) => {
  try {
    const { prompt: originalPrompt, quality = 'standard', size = '1536x1024' } = req.body;

    // Map frontend quality values to OpenAI's expected values
    const qualityMapping = {
      'low': 'standard',
      'medium': 'standard',
      'high': 'hd',
      'standard': 'standard',
      'hd': 'hd'
    };

    const openaiQuality = qualityMapping[quality] || 'standard';

    // Optimize prompt length for OpenAI's limits
    const prompt = optimizePrompt(originalPrompt);

    console.log('Generating image with prompt:', prompt.substring(0, 100) + '...');
    console.log('Frontend Quality:', quality, '-> OpenAI Quality:', openaiQuality, 'Size:', size);
    console.log('Original prompt length:', originalPrompt.length, 'Optimized length:', prompt.length);
    
    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: "gpt-image-1",
        prompt: prompt,
        n: 1,
        size: size,
        quality: quality,
        response_format: "b64_json"
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API Error:', errorData);
      
      return res.status(response.status).json({
        error: 'Image generation failed',
        message: errorData.error?.message || 'Unknown error occurred',
        code: errorData.error?.code
      });
    }

    const data = await response.json();
    const b64Image = data.data[0]?.b64_json;
    
    if (!b64Image) {
      throw new Error('No image data received from OpenAI');
    }

    res.json({
      success: true,
      image: `data:image/png;base64,${b64Image}`,
      model: "gpt-image-1"
    });

  } catch (error) {
    console.error('Error in image generation:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to generate image'
    });
  }
});

// OpenAI Chat Completion endpoint for prompt enhancement
app.post('/api/enhance-prompt', async (req, res) => {
  try {
    const { prompt } = req.body;
    
    if (!prompt || typeof prompt !== 'string') {
      return res.status(400).json({ error: 'Valid prompt is required' });
    }
    
    if (prompt.length > 200) {
      return res.status(400).json({ error: 'Prompt too long for enhancement (max 200 characters)' });
    }
    
    console.log('Enhancing prompt:', prompt);
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo-1106',
        messages: [
          {
            role: 'system',
            content: [
              'You are an expert YouTube copywriter. ',
              'Rewrite the user\'s rough video idea into a SINGLE professional, catchy, YouTube-ready title. ',
              'Key rules:\n',
              '• Keep it under 90 characters.\n',
              '• Use Title Case capitalization.\n',
              '• Do NOT wrap the title in quotes or markdown.\n',
              '• The title must stay faithful to the user\'s original topic and intent.\n',
              '• Return ONLY the title text.'
            ].join('')
          },
          {
            role: 'user',
            content: prompt.trim().slice(0, 200)
          }
        ],
        temperature: 0.7,
        max_tokens: 20,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI Chat API Error:', errorData);
      
      return res.status(response.status).json({
        error: 'Prompt enhancement failed',
        message: errorData.error?.message || 'Unknown error occurred'
      });
    }

    const data = await response.json();
    const enhancedPrompt = data.choices?.[0]?.message?.content || '';
    
    // Clean up any stray quotes/newlines
    const cleanPrompt = enhancedPrompt.trim().replace(/^"|"$/g, '').replace(/^'|'$/g, '');
    
    res.json({
      success: true,
      enhancedPrompt: cleanPrompt,
      originalPrompt: prompt
    });

  } catch (error) {
    console.error('Error in prompt enhancement:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to enhance prompt'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: 'API endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Secure API server running on port ${PORT}`);
  console.log(`🔒 CORS enabled for: ${process.env.CORS_ORIGIN || 'http://localhost:3000'}`);
  console.log(`⚡ Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Verify environment variables
  if (!process.env.OPENAI_API_KEY) {
    console.warn('⚠️  WARNING: OPENAI_API_KEY not found in environment variables');
  } else {
    console.log('✅ OpenAI API key loaded');
  }
});

module.exports = app;
